# 后端请求地址
VUE_APP_BASE_URL='http://127.0.0.1:9086/csm'

# 接口超时时间
VUE_APP_TIME_OUT=15000

# 是否全局加签名 0:否   1:是
VUE_APP_GLOBAL_SIGN=0

# 是否全局加密 0:否   1:是
VUE_APP_GLOBAL_CRYPT=0

# 是否全局解密 0:否   1:是
VUE_APP_GLOBAL_DECRYPT=0

# 是否全局对入参做序列化处理 0:否   1:是
VUE_APP_GLOBAL_PARAMS_STRINGIFY=0

# 加密算法 sm4、aes、rsa
VUE_APP_CRYPT_TYPE='sm4'

# 极简 0:极简  1:hos  2:纯净版pure
VUE_APP_SIMPLE_ONCE='0'

# 是否显示系统特色，当 VUE_APP_SIMPLE_ONCE 为 0 时生效。 1:显示  0:不显示
VUE_APP_IS_FEATURE=0

# 环境 0: 独立运行  1: 注册到门户引擎中运行
VUE_APP_ENV_TYPE = '0'

# 页面标题
VUE_APP_TITLE = '科研平台 V4.0'

# 极简版导航栏是否显示设置按钮 0:否   1:是
VUE_APP_NAV_SETUP = '1'

#帮助文档链接
VUE_APP_HELP_URL = 'http://114.242.246.250:8034/'

# 登录类型(form:表单登录 oauth:单点登录)
VUE_APP_LOGIN_TYPE = 'form'

# 统一登录平台的编码
VUE_APP_LOGIN_SOURCE = 'hos'

# 统一登录测试地址 发送 跨域 token
VUE_APP_OAUTH_URL = /oauth/postMessage

# 登录类型为oauth类型时需要
VUE_APP_LOGIN_OAUTH_SERVER_URL = '/oauth/logout'

# 统一登录修改密码地址
VUE_APP_OAUTH_UPDATE_PWD_URL = '/setpassword'

# pageOffice链接
VUE_APP_PAGE_OFFICE_API = '/csm'

# 右上角MSG_BELL开关	'0':关闭	'1':开启
VUE_APP_MSG_BELL = '1'

# 消息中心ws直连或代理	'/ws':代理	'ip地址':直连
VUE_APP_MSG_WS = '/ws'

# 超时重定向开启 true/false
VUE_APP_TIMEOUT_REDIRECT=false

# 是否引入打印 true/false
VUE_APP_USE_PRINT=false

# 是否引入pageOffice打印 true/false
VUE_APP_USE_PAGE_OFFICE=false