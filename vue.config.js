// fix-hos 添加不同的模式，用于区分main和his入组模式
const mode = process.env.VUE_APP_MODE || 'main'; // 打包模式 his/main
console.log('=================== mode: ' + mode + ', env: ' + process.env.NODE_ENV + ' ========================')
const Version = new Date().getTime() // 版本 一个随机数放到文件名后边，避免线上缓存
const path = require('path')
const CopyWebpackPlugin = require('copy-webpack-plugin') // 这里引入`这个CopyWebpackPlugin`插件
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const FileManagerPlugin = require('filemanager-webpack-plugin') // 打包生成zip文件
const GenerateAssetPlugin = require('generate-asset-webpack-plugin') // 生成版本号文件
// const CompressionPlugin = require('compression-webpack-plugin') // gzip压缩
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin // 打包结果分析
const EditHtmlContentWebpackPlugin = require('./_webpack-plugins/EditHtmlContentWebpackPlugin')
const TerserPlugin = require('terser-webpack-plugin')
const userConfig = require('./vue.config.user') // 引入用户自定义配置
const APP_VERSION = require('./package.json').version
const APP_NAME = mode == 'his' ? 'hos-app-his' : require('./package.json').name
const APP_BUILD_DATE = getNowFormatDate()
const APP_FULL_NAME = APP_NAME + '-' + APP_VERSION + '.' + APP_BUILD_DATE

function getNowFormatDate() {
  var date = new Date()
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var strDate = date.getDate()
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate
  }
  var currentdate = `${year}${month}${strDate}`
  return currentdate
}

function resolve(dir) {
  return path.join(__dirname, '/src' + '/' + dir)
}
const name = process.env.VUE_APP_TITLE || 'hos' // 网页标题
let port = process.env.port || process.env.npm_config_port || 9087 // 端口
let publicPath = process.env.BASE_URL || '/'
if(mode == 'his'){
  port = 9088
}
const getEditHtmlContentWebpackPlugin = () => {
  let result = []
  if (process.env.npm_config_pb) {
    console.log('加入前缀构建过程====')
    result.push(new EditHtmlContentWebpackPlugin())
  }
  return result
}
// 在开发环境下,针对ws可能连接失败导致node进程运行停止,对process进行未捕获异常处理
if (process.env.NODE_ENV === 'development') {
  process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err)
  })
}
module.exports = {
  publicPath,
  lintOnSave: false,
  configureWebpack: {
    // fix-hos
    entry: mode == 'his' ? './src/his/main.js' : './src/main.js',
    // 优化
    optimization: {
      minimizer: process.env.NODE_ENV === 'production' ? [
        new TerserPlugin({
          terserOptions: {
            compress: {
              // 压缩选项
              drop_console: true, // 是否删除console语句
              drop_debugger: true // 是否删除debugger语句
            },
            format: {
              comments: false // 删除所有注释
            },
            ecma: 8, // 支持ES8语法
            safari10: true // 兼容Safari 10
          },
          extractComments: false, // 不将注释提取到单独的文件
          parallel: true, // 并行处理以提高性能
          sourceMap: false // 生成source map，便于调试
        })
      ] : [],
      // fix-hos
      splitChunks: process.env.NODE_ENV === 'production' ? {
        chunks: 'all',
        minSize: 20000,
        maxSize: 0,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        automaticNameDelimiter: '~',
        name: false,
        cacheGroups: {
          hosui: {
            name: `hosui`,
            test: /[\\/]node_modules[\\/]hosui[\\/]/,
            chunks: 'all',
            priority: 20,
            enforce: true
          },
          echarts: {
            name: `echarts`,
            test: /[\\/]node_modules[\\/]echarts[\\/]/,
            chunks: 'all',
            priority: 1,
            enforce: true
          },
          vueVendor: {
            name: 'vue-vendors',
            test: /[\\/]node_modules[\\/](vue|vuex|vue-router|vue-i18n)[\\/]/,
            chunks: 'all',
            priority: 10,
            enforce: true
          }
        }
      } : {}
    },
    plugins: [
      new CopyWebpackPlugin([
        {
          from: path.resolve(__dirname, 'static'),
          to: 'static',
          ignore: ['.*']
        }
      ]),
      // new CompressionPlugin({
      //   test: /\.(js|css|html)?$/i, // 压缩文件格式
      //   filename: '[path].gz[query]', // 压缩后的文件名
      //   algorithm: 'gzip', // 使用gzip压缩
      //   minRatio: 0.8 // 压缩率小于1才会压缩
      // }),
      ...getEditHtmlContentWebpackPlugin()
    ],
    output: {
      filename: `js/[name].${Version}.js`,
      chunkFilename: `js/[name].${Version}.js`
    }
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: 'expanded' }
      }
    }
  },
  chainWebpack: (config) => {
    config.plugin('html').tap((args) => {
      args[0].title = name
      // fix-hos
      args[0].mode = mode
      return args
    })

    // 只加载当前页面需要的js
    config.plugins.delete('prefetch')

    config
      .plugin('extract-css')
      .use(MiniCssExtractPlugin, [
        {
          filename: `css/[name].${Version}.css`,
          chunkFilename: `css/[name].${Version}.css`,
        }
      ])
    if (process.env.npm_config_report) {
      config.plugin('bundle-analyzer').use(BundleAnalyzerPlugin, [
        {
          defaultSizes: 'gzip', //显示大小的类型：'stat,parsed,gzip'
          analyzerMode: 'static', //分析模式:'static','server','disabled'
          openAnalyzer: true, //打包后自动打开报告页面
          reportFilename: 'bundle-report.html' //输出报告文件的文件名
        }
      ])
    }
    if (process.env.NODE_ENV === 'production') {
      config
        .plugin('fileManager')
        .use(FileManagerPlugin, [
          {
            onStart: {
              delete: ['./' + APP_NAME, './' + APP_FULL_NAME + '.zip']
            },
            onEnd: {
              copy: mode == 'his' ? [
                { source: './dist/index.html', destination: './' + APP_NAME + '/dist/index.html' },
                { source: './dist/js', destination: './' + APP_NAME + '/dist/js' },
                { source: './dist/css', destination: './' + APP_NAME + '/dist/css' },
                { source: './dist/fonts', destination: './' + APP_NAME + '/dist/fonts' },
              ] : [{ source: './dist', destination: './' + APP_NAME + '/dist' }],
              delete: ['./dist'],
              archive: [{ source: './' + APP_NAME, destination: './' + APP_FULL_NAME + '.zip' }]
            }
          }
        ])
        .after('html')

      config
        .plugin('generateAsset')
        .use(GenerateAssetPlugin, [
          {
            filename: 'version',
            fn: (compilation, cb) => {
              cb(null, APP_FULL_NAME)
            },
            extraFiles: []
          }
        ])
        .after('fileManager')
    }
    config.resolve.alias
      .set('@', resolve('biz'))
      .set('@his', resolve('his'))
      .set('@src', resolve('./'))
      .set('@root', resolve('../'))
      .set('@core', resolve('sys'))
      .set('@base', resolve('sys/hos-app-base'))
      .set('@components', resolve('sys/hos-app-base/components'))
  },
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    hot: true,
    disableHostCheck: true,
    proxy: {
      '/csm/analyzer': {
        // 开发环境后端地址
        target: 'http://127.0.0.1:5001/api/v1',
        changeOrigin: true,
        pathRewrite: {
          '^/csm/analyzer': '/'
        }
      },
      '/csm': {
        // 开发环境后端地址
        target: 'http://localhost:9086/csm',
        // target: 'http://**********:8088/csm',
        // target: 'http://**************:8167/api', // 打印设计器
        // target: 'http://*************:8367/api', // 元数据
        // target: 'http://**************:8367/api', // 张珂
        // target: 'http://**************:8167/api', // 赵
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/csm': '/'
        }
      }
    }
  },
  transpileDependencies:
    process.env.NODE_ENV === 'production' ? ['sm-crypto', 'canvg', 'monaco-editor'] : ['monaco-editor'],
  ...userConfig
}
